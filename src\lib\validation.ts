import type { UniverAPI, ValidationRule, ValidationResult } from './types/univer'
import { log } from './logger'

/**
 * 验证任务 - 简化的函数式接口
 */
export async function validateTask(univerAPI: UniverAPI, validationRule: ValidationRule): Promise<ValidationResult> {
  try {
    console.log('🔍 开始验证任务:', validationRule)
    log.debug('开始验证任务:', validationRule)

    const service = new ExcelValidationService(univerAPI)

    let result: ValidationResult
    switch (validationRule.type) {
      case 'cellValue':
      case 'input':
        result = await service.validateCellValue(validationRule)
        break
      case 'cellStyle':
        result = await service.validateCellStyle(validationRule)
        break
      case 'cellFormula':
        result = await service.validateCellFormula(validationRule)
        break
      case 'cellFormat':
        result = await service.validateCellFormat(validationRule)
        break
      case 'chart':
        result = await service.validateChart(validationRule)
        break
      case 'pivotTable':
        result = await service.validatePivotTable(validationRule)
        break
      case 'filter':
        result = await service.validateFilter(validationRule)
        break
      case 'sort':
        result = await service.validateSort(validationRule)
        break
      case 'multiSort':
        result = await service.validateMultiSort(validationRule)
        break
      case 'conditional_format':
      case 'conditionalFormat':
      case 'multiConditionalFormat':
        result = await service.validateConditionalFormat(validationRule)
        break
      case 'multiCellAlignment':
        result = await service.validateMultiCellAlignment(validationRule)
        break
      case 'multiBorder':
        result = await service.validateMultiBorder(validationRule)
        break
      case 'dataValidation':
        result = await service.validateDataValidation(validationRule)
        break
      case 'cellMerge':
        result = await service.validateCellMerge(validationRule)
        break
      case 'textWrap':
        result = await service.validateTextWrap(validationRule)
        break
      case 'formulaFill':
        result = await service.validateFormulaFill(validationRule)
        break
      default:
        result = {
          success: false,
          message: `不支持的验证类型: ${validationRule.type}`
        }
    }

    console.log('📊 验证结果:', result)
    return result
  } catch (error) {
    console.error('❌ 验证任务时发生错误:', error)
    log.error('验证任务时发生错误:', error)
    return {
      success: false,
      message: '验证过程中发生错误，请重试'
    }
  }
}

/**
 * Excel验证服务 - 基于官方Univer API
 */
export class ExcelValidationService {
  constructor(private univerAPI: UniverAPI) {}

  /**
   * 提取 Univer 单元格的实际值
   */
  private extractCellValue(cellValue: unknown): unknown {
    if (cellValue && typeof cellValue === 'object' && 'v' in cellValue) {
      return (cellValue as { v: unknown }).v;
    }
    return cellValue;
  }

  /**
   * 比较两个值是否相等
   */
  private compareValues(actual: unknown, expected: unknown): boolean {
    // 处理数字比较
    if (typeof expected === 'number' && typeof actual === 'number') {
      return Math.abs(actual - expected) < 0.0001 // 浮点数比较
    }

    // 处理字符串比较（忽略大小写和前后空格）
    if (typeof expected === 'string' && typeof actual === 'string') {
      return actual.trim().toLowerCase() === expected.trim().toLowerCase()
    }

    // 其他类型直接比较
    return actual === expected
  }

  /**
   * 验证单元格值
   */
  async validateCellValue(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || rule.expectedValue === undefined) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望值'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) throw new Error('未获取到工作簿');
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取活动工作表'
        }
      }

      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      const cellValue = range.getValue()
      const actualValue = this.extractCellValue(cellValue)

      // 类型转换和比较
      const expectedValue = rule.expectedValue
      const isMatch = this.compareValues(actualValue, expectedValue)

      return {
        success: isMatch,
        message: isMatch
          ? '单元格值验证通过！'
          : `单元格 ${rule.cell} 的值不正确。期望: "${expectedValue}"，实际: "${actualValue}"`,
        details: {
          cell: rule.cell,
          expected: expectedValue,
          actual: actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证单元格值时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格样式
   */
  async validateCellStyle(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedStyle) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望样式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }
      const range = worksheet.getRange(rule.cell)

      // 获取单元格数据和样式信息
      const cellData = range.getCellData()
      let style: any = {}

      // 根据Univer文档，样式可能是ID引用或直接的样式对象
      if (cellData?.s) {
        if (typeof cellData.s === 'string') {
          // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
          const workbook = this.univerAPI.getActiveWorkbook()
          let styles = {}

          try {
            // 使用save()方法获取工作簿数据，按照文档建议
            try {
              const workbookData = await (workbook as unknown as { save(): Promise<{ styles?: Record<string, any> }> }).save()
              styles = workbookData?.styles || {}
              log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
            } catch (saveError) {
              log.debug('save()方法失败，尝试其他方法:', saveError)

              // 备用方法：尝试从Univer实例获取
              const univerInstance = (this.univerAPI as unknown as { _univerInstance?: unknown })?._univerInstance
              if (univerInstance) {
                const currentWorkbook = (univerInstance as { getCurrentUniverSheetInstance(): unknown }).getCurrentUniverSheetInstance()
                const workbookSnapshot = (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.save?.() || (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.getSnapshot?.()
                styles = (workbookSnapshot as { styles?: Record<string, any> })?.styles || {}
              }
            }

            log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
          } catch (error) {
            log.debug('获取样式表失败:', error)
          }

          style = styles[cellData.s] || {}
          log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!styles[cellData.s] })
        } else {
          // 如果是对象，直接使用
          style = cellData.s
          log.debug('直接使用样式对象:', style)
        }
      }

      log.debug('单元格数据和样式:', { cellData, style, styleType: typeof cellData?.s })

      const validationResults = []

      // 验证粗体
      if (rule.expectedStyle.bold !== undefined) {
        // 只检查标准的粗体属性，避免过于宽松的验证
        const isBold = style.bl === 1 || style.bl === true ||
                      style.bold === 1 || style.bold === true ||
                      style.fontWeight === 'bold' || style.fontWeight === 700 ||
                      (style.ft && (style.ft.bl === 1 || style.ft.bl === true))
        const expectedBold = rule.expectedStyle.bold

        log.debug('粗体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isBold: isBold,
          expectedBold: expectedBold,
          styleKeys: Object.keys(style),
          styleType: typeof style
        })

        validationResults.push({
          property: 'bold',
          expected: expectedBold,
          actual: isBold,
          match: isBold === expectedBold
        })
      }

      // 验证斜体
      if (rule.expectedStyle.italic !== undefined) {
        // 支持多种斜体属性格式
        const isItalic = style.it === 1 || style.it === true ||
                         style.italic === 1 || style.italic === true ||
                         style.fontStyle === 'italic' ||
                         (style.ft && (style.ft.it === 1 || style.ft.it === true))
        const expectedItalic = rule.expectedStyle.italic

        log.debug('斜体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isItalic: isItalic,
          expectedItalic: expectedItalic,
          styleKeys: Object.keys(style)
        })

        validationResults.push({
          property: 'italic',
          expected: expectedItalic,
          actual: isItalic,
          match: isItalic === expectedItalic
        })
      }

      // 验证字体系列
      if (rule.expectedStyle.fontFamily) {
        // 从样式对象获取字体系列 (ff属性)
        const actualFontFamily = style.ff || style.fontFamily ||
                                 (style.ft && style.ft.ff) ||
                                 (style.ft && style.ft.fontFamily) || ''
        const expectedFontFamily = rule.expectedStyle.fontFamily

        log.debug('字体系列获取:', { actualFontFamily, expectedFontFamily, style })

        // 字体名称标准化
        const normalizeFont = (font: string) => font.toLowerCase().replace(/['"]/g, '').trim()
        const actualNormalized = normalizeFont(actualFontFamily)
        const expectedNormalized = normalizeFont(expectedFontFamily)

        // 更严格的验证：必须有实际字体且匹配期望字体
        const isMatch = actualNormalized !== '' && actualNormalized === expectedNormalized

        validationResults.push({
          property: 'fontFamily',
          expected: expectedFontFamily,
          actual: actualFontFamily || '(未设置)',
          match: isMatch
        })
      }

      // 验证字体颜色
      if (rule.expectedStyle.color) {
        // 从样式对象获取字体颜色 (cl属性或其他可能的属性)
        const actualColor = style.cl || style.color || style.fc ||
                           (style.ft && style.ft.cl) ||
                           (style.ft && style.ft.color) || ''
        const expectedColor = rule.expectedStyle.color

        console.log('🎨 字体颜色验证:', { actualColor, expectedColor, style })
        log.debug('字体颜色获取:', { actualColor, expectedColor, style })

        // 颜色值标准化和比较
        const normalizeColor = (color: any): string => {
          if (!color) return ''

          // 处理十六进制颜色
          if (typeof color === 'string' && color.startsWith('#')) {
            return color.toLowerCase()
          }

          // 处理RGB对象
          if (typeof color === 'object' && color !== null) {
            const { r, g, b } = color as { r?: number; g?: number; b?: number }
            if (r !== undefined && g !== undefined && b !== undefined) {
              return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
            }
          }

          // 处理其他格式
          return String(color).toLowerCase()
        }

        const normalizedActual = normalizeColor(actualColor)
        const normalizedExpected = normalizeColor(expectedColor)

        // 更严格的验证：必须有实际颜色且匹配期望颜色
        const isMatch = normalizedActual !== '' && normalizedActual === normalizedExpected

        console.log('🎨 字体颜色比较:', { normalizedActual, normalizedExpected, isMatch })

        validationResults.push({
          property: 'color',
          expected: expectedColor,
          actual: actualColor || '(未设置)',
          match: isMatch
        })
      }

      // 验证背景颜色
      if (rule.expectedStyle.backgroundColor) {
        // 从样式对象获取背景颜色 (bg属性或其他可能的属性)
        const actualBgColor = style.bg || style.backgroundColor || style.bc ||
                             (style.ft && style.ft.bg) ||
                             (style.ft && style.ft.backgroundColor) || ''
        const expectedBgColor = rule.expectedStyle.backgroundColor

        console.log('🎨 背景颜色验证:', { actualBgColor, expectedBgColor, style })
        log.debug('背景颜色获取:', { actualBgColor, expectedBgColor, style })

        // 颜色值标准化和比较
        const normalizeColor = (color: any): string => {
          if (!color) return ''

          // 处理十六进制颜色
          if (typeof color === 'string' && color.startsWith('#')) {
            return color.toLowerCase()
          }

          // 处理RGB对象
          if (typeof color === 'object' && color !== null) {
            const { r, g, b } = color as { r?: number; g?: number; b?: number }
            if (r !== undefined && g !== undefined && b !== undefined) {
              return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
            }
          }

          // 处理其他格式
          return String(color).toLowerCase()
        }

        const normalizedActual = normalizeColor(actualBgColor)
        const normalizedExpected = normalizeColor(expectedBgColor)

        // 更严格的验证：必须有实际背景颜色且匹配期望颜色
        const isMatch = normalizedActual !== '' && normalizedActual === normalizedExpected

        console.log('🎨 背景颜色比较:', { normalizedActual, normalizedExpected, isMatch })

        validationResults.push({
          property: 'backgroundColor',
          expected: expectedBgColor,
          actual: actualBgColor || '(未设置)',
          match: isMatch
        })
      }

      // 检查是否有任何验证项目
      if (validationResults.length === 0) {
        return {
          success: false,
          message: `验证规则配置错误：没有指定要验证的样式属性`,
          details: { expectedStyle: rule.expectedStyle }
        }
      }

      // 检查所有验证结果
      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '样式验证通过！',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.property}: 期望 ${result.expected}, 实际 ${result.actual}`
        ).join('; ')

        return {
          success: false,
          message: `单元格 ${rule.cell} 的样式不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证样式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格公式
   */
  async validateCellFormula(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const range = worksheet.getRange(rule.cell)
      const cellData = range.getCellData()

      // 获取公式
      const actualFormula = cellData.f || ''
      const expectedFormula = rule.expectedFormula || ''

      log.debug('公式验证:', {
        cell: rule.cell,
        actualFormula,
        expectedFormula,
        cellData
      })

      // 标准化公式比较（去除空格，统一大小写）
      const normalizeFormula = (formula: string) => {
        return formula.replace(/\s+/g, '').toUpperCase()
      }

      const normalizedActual = normalizeFormula(actualFormula)
      const normalizedExpected = normalizeFormula(expectedFormula)

      let formulaMatch = false
      if (expectedFormula) {
        formulaMatch = normalizedActual === normalizedExpected
      }

      // 验证计算结果
      let valueMatch = true
      if (rule.expectedValue !== undefined) {
        const actualValue = range.getValue()
        const extractedValue = this.extractCellValue(actualValue)
        valueMatch = this.compareValues(extractedValue, rule.expectedValue)
      }

      const success = (!expectedFormula || formulaMatch) && valueMatch

      if (success) {
        return {
          success: true,
          message: '公式验证通过！',
          details: {
            cell: rule.cell,
            formula: actualFormula,
            value: range.getValue()
          }
        }
      } else {
        let message = `单元格 ${rule.cell} 的公式验证失败。`
        if (expectedFormula && !formulaMatch) {
          message += `\n期望公式: ${expectedFormula}\n实际公式: ${actualFormula}`
        }
        if (rule.expectedValue !== undefined && !valueMatch) {
          message += `\n期望结果: ${rule.expectedValue}\n实际结果: ${range.getValue()}`
        }

        return {
          success: false,
          message,
          details: {
            cell: rule.cell,
            expectedFormula,
            actualFormula,
            expectedValue: rule.expectedValue,
            actualValue: range.getValue()
          }
        }
      }
    } catch (error) {
      log.error('验证公式时发生错误:', error)
      return {
        success: false,
        message: `验证公式时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证单元格格式
   */
  async validateCellFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cell) {
        return {
          success: false,
          message: '缺少单元格位置信息'
        }
      }

      const range = worksheet.getRange(rule.cell)
      const format = range.getNumberFormat()

      log.debug(`单元格 ${rule.cell} 的格式:`, format)

      if (rule.expectedFormat) {
        // 对于货币格式，需要更严格的验证
        if (rule.expectedFormat === 'currency') {
          // 检查是否为货币格式的各种可能表示
          const currencyFormats = [
            '¥#,##0.00',
            '¥#,##0',
            '$#,##0.00',
            '$#,##0',
            '￥#,##0.00',
            '￥#,##0',
            '"¥"#,##0.00',
            '"$"#,##0.00',
            '"￥"#,##0.00',
            // 更多可能的货币格式
            '[$¥-804]#,##0.00',
            '[$￥-804]#,##0.00',
            '[$¥-804]#,##0',
            '[$￥-804]#,##0'
          ]

          const isCurrencyFormat = currencyFormats.some(cf =>
            format === cf ||
            format.includes('¥') ||
            format.includes('$') ||
            format.includes('￥') ||
            format.includes('currency')
          )

          if (!isCurrencyFormat) {
            return {
              success: false,
              message: `单元格 ${rule.cell} 的格式不是货币格式。请按照操作步骤设置货币格式：\n1. 选择单元格 ${rule.cell}\n2. 切换到"数据"菜单\n3. 点击"货币"格式按钮\n\n当前格式: ${format}`
            }
          }
        } else {
          // 对于其他格式，进行精确匹配
          if (format !== rule.expectedFormat) {
            return {
              success: false,
              message: `单元格 ${rule.cell} 的格式不正确。期望: ${rule.expectedFormat}, 实际: ${format}`
            }
          }
        }
      }

      return {
        success: true,
        message: '单元格格式验证通过'
      }
    } catch (error) {
      log.error('验证单元格格式时发生错误:', error)
      return {
        success: false,
        message: '验证单元格格式时发生错误'
      }
    }
  }

  /**
   * 验证图表
   */
  async validateChart(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 检查DOM中是否存在图表相关元素
      const chartSelectors = [
        '.univer-chart',
        '.echarts-chart',
        '.chart-container',
        '[data-chart-id]',
        'canvas[data-zr-dom-id]', // ECharts canvas
        '.univer-drawing-object' // Univer绘图对象
      ]

      let chartFound = false
      for (const selector of chartSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          chartFound = true
          log.debug(`找到图表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      if (!chartFound) {
        return {
          success: false,
          message: '未找到图表。请按照操作步骤创建图表：\n1. 选择数据范围\n2. 点击"插入"选项卡\n3. 选择"图表"\n4. 选择合适的图表类型'
        }
      }

      return {
        success: true,
        message: '图表创建成功！任务完成。'
      }

    } catch (error) {
      log.error('图表验证错误:', error)
      return {
        success: false,
        message: `图表验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证数据透视表
   */
  async validatePivotTable(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      console.log('🔍 开始验证数据透视表:', rule)

      // 检查DOM中是否存在透视表相关元素
      const pivotSelectors = [
        '.univer-pivot-table',
        '.pivot-table',
        '[data-pivot-id]',
        '.univer-pivot'
      ]

      let pivotFound = false
      for (const selector of pivotSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          pivotFound = true
          console.log(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          log.debug(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      // 检查当前工作表中是否有透视表的特征数据结构
      let hasPivotStructure = false
      let pivotData = null
      let structureValidation = null

      try {
        const pivotRange = rule.pivotRange || 'A1:Z50'
        const range = worksheet.getRange(pivotRange)
        const values = range.getValues()

        console.log('📊 检查透视表数据结构:', {
          pivotRange,
          values: values?.slice(0, 10), // 只显示前10行
          expectedStructure: rule.expectedStructure
        })

        if (values && values.length > 0) {
          pivotData = values

          // 如果有期望结构，必须严格验证结构
          if (rule.expectedStructure) {
            structureValidation = this.validatePivotStructure(values, rule.expectedStructure)

            // 只有结构验证通过才认为有透视表
            if (structureValidation.isValid) {
              hasPivotStructure = true
              console.log('透视表结构验证通过')
            } else {
              console.log('透视表结构验证失败:', structureValidation.message)
            }
          } else {
            // 如果没有期望结构，使用宽松的关键字检查（但仍需要多个条件）
            let keywordCount = 0
            const foundKeywords = []

            for (let i = 0; i < Math.min(values.length, 20); i++) {
              for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                const cellValue = values[i][j]
                if (cellValue && typeof cellValue === 'string') {
                  const pivotKeywords = ['总计', '小计', '求和', '计数', '平均值', '最大值', '最小值', 'Sum', 'Count', 'Average', 'Total']
                  const matchedKeyword = pivotKeywords.find(keyword => cellValue.includes(keyword))
                  if (matchedKeyword && !foundKeywords.includes(matchedKeyword)) {
                    foundKeywords.push(matchedKeyword)
                    keywordCount++
                  }
                }
              }
            }

            // 需要至少找到2个不同的透视表关键字才认为是透视表
            hasPivotStructure = keywordCount >= 2
            console.log(`找到透视表关键字: ${foundKeywords.join(', ')}, 数量: ${keywordCount}`)
          }
        }
      } catch (e) {
        console.error('检查透视表结构失败:', e)
        log.debug('检查透视表结构失败:', e)
      }

      const pivotCreated = pivotFound || hasPivotStructure

      if (!pivotCreated) {
        // 如果有期望结构但验证失败，返回具体的失败原因
        if (rule.expectedStructure && structureValidation && !structureValidation.isValid) {
          return {
            success: false,
            message: `数据透视表结构不正确。${structureValidation.message}`,
            details: {
              expectedStructure: rule.expectedStructure,
              actualData: pivotData?.slice(0, 10),
              structureValidation: structureValidation
            }
          }
        }

        return {
          success: false,
          message: '未检测到数据透视表。请按照操作步骤创建透视表：\n1. 选择数据范围\n2. 右键点击选择"数据透视表"或通过"插入"菜单\n3. 确认数据范围并选择放置位置\n4. 设置行字段、列字段和值字段\n5. 点击"确定"创建透视表',
          details: {
            expectedStructure: rule.expectedStructure,
            pivotRange: rule.pivotRange,
            structureValidation: structureValidation
          }
        }
      }

      return {
        success: true,
        message: '数据透视表验证成功！透视表结构和内容都正确。',
        details: {
          hasPivotStructure,
          pivotFound,
          expectedStructure: rule.expectedStructure,
          actualData: pivotData?.slice(0, 5), // 只返回前5行作为示例
          structureValidation: structureValidation
        }
      }

    } catch (error) {
      console.error('❌ 透视表验证错误:', error)
      log.error('透视表验证错误:', error)
      return {
        success: false,
        message: `透视表验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证透视表结构
   */
  private validatePivotStructure(data: any[][], expectedStructure: any): { isValid: boolean; message: string } {
    try {
      console.log('🏗️ 验证透视表结构:', { data: data.slice(0, 5), expectedStructure })

      if (!expectedStructure) {
        return { isValid: true, message: '无结构要求' }
      }

      const validationResults = []

      // 检查行标题 - 必须全部存在
      if (expectedStructure.rowHeaders && Array.isArray(expectedStructure.rowHeaders)) {
        const foundRowHeaders = []
        for (const expectedHeader of expectedStructure.rowHeaders) {
          let found = false
          for (let i = 0; i < Math.min(data.length, 15); i++) {
            for (let j = 0; j < Math.min(data[i].length, 15); j++) {
              if (data[i][j] === expectedHeader) {
                found = true
                foundRowHeaders.push(expectedHeader)
                break
              }
            }
            if (found) break
          }
          if (!found) {
            validationResults.push(`缺少行标题: ${expectedHeader}`)
          }
        }
        console.log('行标题验证:', { expected: expectedStructure.rowHeaders, found: foundRowHeaders })
      }

      // 检查列标题 - 必须全部存在
      if (expectedStructure.columnHeaders && Array.isArray(expectedStructure.columnHeaders)) {
        const foundColumnHeaders = []
        for (const expectedHeader of expectedStructure.columnHeaders) {
          let found = false
          for (let i = 0; i < Math.min(data.length, 15); i++) {
            for (let j = 0; j < Math.min(data[i].length, 15); j++) {
              if (data[i][j] === expectedHeader) {
                found = true
                foundColumnHeaders.push(expectedHeader)
                break
              }
            }
            if (found) break
          }
          if (!found) {
            validationResults.push(`缺少列标题: ${expectedHeader}`)
          }
        }
        console.log('列标题验证:', { expected: expectedStructure.columnHeaders, found: foundColumnHeaders })
      }

      // 检查期望的总计值
      if (expectedStructure.expectedTotalValue !== undefined) {
        let totalFound = false
        for (let i = 0; i < Math.min(data.length, 15); i++) {
          for (let j = 0; j < Math.min(data[i].length, 15); j++) {
            const cellValue = data[i][j]
            if (typeof cellValue === 'number' && cellValue === expectedStructure.expectedTotalValue) {
              totalFound = true
              break
            }
          }
          if (totalFound) break
        }
        if (!totalFound) {
          validationResults.push(`缺少期望的总计值: ${expectedStructure.expectedTotalValue}`)
        }
        console.log('总计值验证:', { expected: expectedStructure.expectedTotalValue, found: totalFound })
      }

      // 如果是严格验证模式，所有条件都必须满足
      if (expectedStructure.strictValidation && validationResults.length > 0) {
        return {
          isValid: false,
          message: `透视表结构验证失败: ${validationResults.join('; ')}`
        }
      }

      // 非严格模式下，至少要满足一半的条件
      const totalChecks = (expectedStructure.rowHeaders?.length || 0) +
                         (expectedStructure.columnHeaders?.length || 0) +
                         (expectedStructure.expectedTotalValue !== undefined ? 1 : 0)
      const failedChecks = validationResults.length
      const successRate = totalChecks > 0 ? (totalChecks - failedChecks) / totalChecks : 1

      if (successRate < 0.5) {
        return {
          isValid: false,
          message: `透视表结构验证失败: ${validationResults.join('; ')}`
        }
      }

      return { isValid: true, message: '结构验证通过' }

    } catch (error) {
      console.error('透视表结构验证错误:', error)
      return {
        isValid: false,
        message: `结构验证失败: ${error}`
      }
    }
  }

  /**
   * 验证筛选
   */
  async validateFilter(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      console.log('🔍 开始验证筛选:', rule)

      // 使用官方FFilter API进行验证
      const filter = worksheet.getFilter()
      console.log('筛选验证 - 获取筛选器:', filter)
      log.debug('筛选验证 - 获取筛选器:', filter)

      if (!filter) {
        return {
          success: false,
          message: '未检测到筛选器。请确保已正确执行以下步骤：\n1. 选择数据范围（包含表头）\n2. 启用筛选功能（数据 → 筛选）\n3. 设置筛选条件并应用筛选',
          details: { expectedVisibleRows: rule.expectedVisibleRows }
        }
      }

      // 验证筛选结果
      if (rule.expectedFilteredData && Array.isArray(rule.expectedFilteredData)) {
        // 获取筛选后的可见数据
        const dataRange = rule.dataRange || 'A1:Z100'
        const range = worksheet.getRange(dataRange)
        const allData = range.getValues()

        console.log('📊 筛选数据验证:', {
          dataRange,
          allData,
          expectedFilteredData: rule.expectedFilteredData,
          expectedVisibleRows: rule.expectedVisibleRows
        })

        if (!allData || allData.length === 0) {
          return {
            success: false,
            message: '无法获取数据范围内容',
            details: { dataRange }
          }
        }

        // 检查可见行数（排除标题行）
        const visibleDataRows = allData.slice(1) // 排除标题行
        const actualVisibleRows = visibleDataRows.length

        if (rule.expectedVisibleRows !== undefined) {
          if (actualVisibleRows !== rule.expectedVisibleRows) {
            return {
              success: false,
              message: `筛选结果不正确。期望显示 ${rule.expectedVisibleRows} 行数据，实际显示 ${actualVisibleRows} 行。请检查筛选条件是否正确设置。`,
              details: {
                expectedVisibleRows: rule.expectedVisibleRows,
                actualVisibleRows,
                actualData: visibleDataRows
              }
            }
          }
        }

        // 验证具体的筛选数据内容
        if (rule.expectedFilteredData.length > 0) {
          const expectedData = rule.expectedFilteredData
          let dataMatches = true
          const mismatchDetails = []

          for (let i = 0; i < expectedData.length; i++) {
            if (i >= visibleDataRows.length) {
              dataMatches = false
              mismatchDetails.push(`缺少第 ${i + 1} 行数据`)
              continue
            }

            const expectedRow = expectedData[i]
            const actualRow = visibleDataRows[i]

            // 比较每一列的数据
            for (const [colIndex, expectedValue] of Object.entries(expectedRow)) {
              const colIdx = parseInt(colIndex)
              const actualValue = actualRow[colIdx]

              if (actualValue !== expectedValue) {
                dataMatches = false
                mismatchDetails.push(`第 ${i + 1} 行第 ${colIdx + 1} 列: 期望 ${expectedValue}, 实际 ${actualValue}`)
              }
            }
          }

          if (!dataMatches) {
            return {
              success: false,
              message: `筛选数据内容不正确。${mismatchDetails.join('; ')}`,
              details: {
                expectedData,
                actualData: visibleDataRows,
                mismatchDetails
              }
            }
          }
        }
      }

      return {
        success: true,
        message: '筛选验证成功！筛选条件和结果都正确。',
        details: {
          hasFilter: true,
          expectedVisibleRows: rule.expectedVisibleRows,
          expectedFilteredData: rule.expectedFilteredData
        }
      }

    } catch (error) {
      console.error('❌ 筛选验证错误:', error)
      log.error('筛选验证错误:', error)
      return {
        success: false,
        message: `筛选验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证排序
   */
  async validateSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      console.log('🔍 开始验证排序:', rule)

      // 如果有期望的顺序，验证具体顺序
      if (rule.expectedOrder && Array.isArray(rule.expectedOrder)) {
        // 根据排序列获取实际数据
        const columnLetter = rule.column || 'A'
        const startRow = 2 // 假设第一行是标题
        const endRow = startRow + rule.expectedOrder.length - 1

        const actualOrder = []
        for (let i = startRow; i <= endRow; i++) {
          const cellAddress = `${columnLetter}${i}`
          const range = worksheet.getRange(cellAddress)
          const cellData = range.getCellData()
          const value = cellData.v || ''
          actualOrder.push(value)
        }

        console.log('📊 排序验证比较:', {
          expectedOrder: rule.expectedOrder,
          actualOrder,
          column: columnLetter
        })

        // 比较实际顺序和期望顺序
        const isMatch = actualOrder.length === rule.expectedOrder.length &&
                       actualOrder.every((value, index) => value === rule.expectedOrder[index])

        if (isMatch) {
          return {
            success: true,
            message: '排序验证通过！数据已按要求排序。',
            details: { expectedOrder: rule.expectedOrder, actualOrder }
          }
        } else {
          return {
            success: false,
            message: `排序不正确。期望顺序: ${rule.expectedOrder.join(', ')}，实际顺序: ${actualOrder.join(', ')}`,
            details: { expectedOrder: rule.expectedOrder, actualOrder }
          }
        }
      }

      // 如果没有期望顺序，检查是否已排序
      if (rule.range) {
        const range = worksheet.getRange(rule.range)
        const values = range.getValues()

        if (values && values.length > 1) {
          // 检查第一列是否已排序
          const firstColumn = values.map(row => row[0]).filter(val => val !== null && val !== undefined)

          if (firstColumn.length > 1) {
            const isAscending = firstColumn.every((val, i) => i === 0 || val >= firstColumn[i - 1])
            const isDescending = firstColumn.every((val, i) => i === 0 || val <= firstColumn[i - 1])

            if (isAscending || isDescending) {
              return {
                success: true,
                message: '排序验证成功！数据已正确排序。'
              }
            }
          }
        }
      }

      return {
        success: false,
        message: '未检测到排序。请确保已正确执行以下步骤：\n1. 选择数据范围\n2. 点击"数据"菜单中的"排序"\n3. 选择排序列和排序方向\n4. 点击"确定"'
      }

    } catch (error) {
      console.error('❌ 排序验证错误:', error)
      log.error('排序验证错误:', error)
      return {
        success: false,
        message: `排序验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证多列排序
   */
  async validateMultiSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      console.log('🔍 开始验证多列排序:', rule)

      // 如果有期望的顺序，验证具体顺序
      if (rule.expectedOrder && Array.isArray(rule.expectedOrder)) {
        // 获取第一列的数据来验证排序结果
        const startRow = 2 // 假设第一行是标题
        const endRow = startRow + rule.expectedOrder.length - 1

        const actualOrder = []
        for (let i = startRow; i <= endRow; i++) {
          const cellAddress = `A${i}` // 假设验证第一列
          const range = worksheet.getRange(cellAddress)
          const cellData = range.getCellData()
          const value = cellData.v || ''
          actualOrder.push(value)
        }

        console.log('📊 多列排序验证比较:', {
          expectedOrder: rule.expectedOrder,
          actualOrder,
          sorts: rule.sorts
        })

        // 比较实际顺序和期望顺序
        const isMatch = actualOrder.length === rule.expectedOrder.length &&
                       actualOrder.every((value, index) => value === rule.expectedOrder[index])

        if (isMatch) {
          return {
            success: true,
            message: '多列排序验证通过！数据已按要求排序。',
            details: { expectedOrder: rule.expectedOrder, actualOrder, sorts: rule.sorts }
          }
        } else {
          return {
            success: false,
            message: `多列排序不正确。期望顺序: ${rule.expectedOrder.join(', ')}，实际顺序: ${actualOrder.join(', ')}`,
            details: { expectedOrder: rule.expectedOrder, actualOrder, sorts: rule.sorts }
          }
        }
      }

      // 如果没有期望顺序，使用单列排序验证
      return await this.validateSort(rule)

    } catch (error) {
      console.error('❌ 多列排序验证错误:', error)
      return {
        success: false,
        message: `多列排序验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证条件格式
   */
  async validateConditionalFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.range) {
        return {
          success: false,
          message: '缺少验证范围信息'
        }
      }

      const range = worksheet.getRange(rule.range)

      try {
        const conditionalRules = range.getConditionalFormattingRules()
        log.debug('获取到的条件格式规则:', conditionalRules)

        if (!conditionalRules || conditionalRules.length === 0) {
          return {
            success: false,
            message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${rule.range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则"\n4. 设置条件和格式\n5. 点击"确定"`
          }
        }

        console.log('🎨 条件格式验证:', {
          rule,
          conditionalRules,
          ruleType: rule.type
        })

        // 处理多条件格式验证
        if (rule.type === 'multiConditionalFormat' && rule.conditions && rule.expectedResults) {
          return await this.validateMultiConditionalFormat(rule, worksheet, conditionalRules)
        }

        // 对于简单条件格式，验证具体的条件和格式
        if (rule.condition && rule.value !== undefined) {
          let hasMatchingRule = false

          for (const cfRule of conditionalRules) {
            console.log('🔍 检查条件格式规则:', cfRule)
            log.debug('检查条件格式规则:', cfRule)

            // 检查条件类型和值
            const ruleCondition = cfRule.condition || cfRule.type
            const ruleValue = cfRule.value || cfRule.formula1 || cfRule.threshold

            // 验证条件类型
            let conditionMatch = false
            if (rule.condition === 'greaterThan' &&
                (ruleCondition === 'greaterThan' || ruleCondition === 'cellValue' || ruleCondition === 'expression')) {
              conditionMatch = true
            }

            // 验证条件值
            let valueMatch = false
            if (ruleValue !== undefined) {
              const numericRuleValue = parseFloat(String(ruleValue))
              const numericExpectedValue = parseFloat(String(rule.value))
              valueMatch = !isNaN(numericRuleValue) && !isNaN(numericExpectedValue) &&
                          numericRuleValue === numericExpectedValue
            }

            console.log('🎯 条件匹配检查:', {
              conditionMatch,
              valueMatch,
              ruleCondition,
              ruleValue,
              expectedCondition: rule.condition,
              expectedValue: rule.value
            })

            if (conditionMatch && valueMatch) {
              hasMatchingRule = true
              break
            }
          }

          if (!hasMatchingRule) {
            return {
              success: false,
              message: `条件格式设置不正确。请确保：\n1. 条件类型为"${rule.condition}"\n2. 条件值为 ${rule.value}\n3. 设置了正确的背景颜色`,
              details: {
                expectedCondition: rule.condition,
                expectedValue: rule.value,
                actualRules: conditionalRules
              }
            }
          }
        }

        // 验证期望的格式化单元格
        if (rule.expectedFormattedCells && Array.isArray(rule.expectedFormattedCells)) {
          for (const cellAddress of rule.expectedFormattedCells) {
            const cellRange = worksheet.getRange(cellAddress)
            const cellData = cellRange.getCellData()

            // 检查单元格是否有条件格式样式
            if (!cellData.s || typeof cellData.s !== 'object') {
              return {
                success: false,
                message: `单元格 ${cellAddress} 没有应用条件格式。请检查条件格式设置是否正确。`
              }
            }
          }
        }

        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      } catch (e) {
        log.debug('获取条件格式规则失败:', e)
        return {
          success: false,
          message: '无法验证条件格式，请确保已正确设置条件格式。'
        }
      }

    } catch (error) {
      log.error('条件格式验证错误:', error)
      return {
        success: false,
        message: `条件格式验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证多条件格式
   */
  private async validateMultiConditionalFormat(rule: ValidationRule, worksheet: any, conditionalRules: any[]): Promise<ValidationResult> {
    try {
      console.log('🎨 开始验证多条件格式:', rule)

      // 检查是否有足够的条件格式规则
      if (!conditionalRules || conditionalRules.length === 0) {
        return {
          success: false,
          message: '未检测到条件格式规则。请设置多个条件格式规则。',
          details: { expectedConditions: rule.conditions }
        }
      }

      // 验证每个期望的结果
      if (rule.expectedResults) {
        for (const [expectedColor, expectedCells] of Object.entries(rule.expectedResults)) {
          if (!Array.isArray(expectedCells)) continue

          for (const cellAddress of expectedCells) {
            const cellRange = worksheet.getRange(cellAddress)
            const cellData = cellRange.getCellData()
            const cellValue = cellData.v || 0

            console.log(`🎯 验证单元格 ${cellAddress}:`, {
              value: cellValue,
              expectedColor,
              style: cellData.s
            })

            // 检查单元格值是否符合条件
            let shouldHaveColor = false
            for (const condition of rule.conditions) {
              if (this.checkConditionMatch(cellValue, condition)) {
                if (condition.color === expectedColor) {
                  shouldHaveColor = true
                  break
                }
              }
            }

            if (shouldHaveColor) {
              // 检查单元格是否有正确的背景颜色
              const actualBgColor = cellData.s?.bg || cellData.s?.backgroundColor || ''
              const normalizedActual = this.normalizeColor(actualBgColor)
              const normalizedExpected = this.normalizeColor(expectedColor)

              if (normalizedActual !== normalizedExpected) {
                return {
                  success: false,
                  message: `单元格 ${cellAddress} 的条件格式颜色不正确。期望: ${expectedColor}, 实际: ${actualBgColor}`,
                  details: {
                    cell: cellAddress,
                    value: cellValue,
                    expectedColor,
                    actualColor: actualBgColor
                  }
                }
              }
            }
          }
        }
      }

      return {
        success: true,
        message: '多条件格式验证通过！',
        details: {
          conditions: rule.conditions,
          expectedResults: rule.expectedResults,
          actualRules: conditionalRules
        }
      }

    } catch (error) {
      console.error('❌ 多条件格式验证错误:', error)
      return {
        success: false,
        message: `多条件格式验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 检查条件是否匹配
   */
  private checkConditionMatch(value: number, condition: any): boolean {
    const numValue = parseFloat(String(value))
    if (isNaN(numValue)) return false

    switch (condition.type) {
      case 'greaterThanOrEqual':
        return numValue >= condition.value
      case 'between':
        return numValue >= condition.minValue && numValue <= condition.maxValue
      case 'lessThan':
        return numValue < condition.value
      case 'greaterThan':
        return numValue > condition.value
      default:
        return false
    }
  }

  /**
   * 验证多单元格对齐
   */
  async validateMultiCellAlignment(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cells || !Array.isArray(rule.cells)) {
        return {
          success: false,
          message: '验证规则配置错误：缺少单元格对齐配置'
        }
      }

      console.log('🔍 开始验证多单元格对齐:', rule)

      const validationResults = []

      for (const cellConfig of rule.cells) {
        if (!cellConfig.cell || !cellConfig.expectedAlignment) {
          validationResults.push({
            cell: cellConfig.cell || '未知',
            expected: cellConfig.expectedAlignment || '未知',
            actual: '配置错误',
            match: false
          })
          continue
        }

        const range = worksheet.getRange(cellConfig.cell)
        const cellData = range.getCellData()
        const style = cellData.s || {}

        // 获取对齐方式 (ht: 水平对齐, vt: 垂直对齐)
        const actualAlignment = style.ht || style.horizontalAlignment ||
                               (style.alignment && style.alignment.horizontal) || null
        const expectedAlignment = cellConfig.expectedAlignment

        console.log(`📐 单元格 ${cellConfig.cell} 对齐验证:`, {
          actualAlignment,
          expectedAlignment,
          style
        })

        // 对齐方式映射 - 支持数字和字符串格式
        const alignmentMap: Record<string, number> = {
          'left': 1,
          'center': 2,
          'right': 3,
          'justify': 4
        }

        const reverseAlignmentMap: Record<number, string> = {
          1: 'left',
          2: 'center',
          3: 'right',
          4: 'justify'
        }

        // 标准化对齐方式
        let normalizedActual: string | null = null
        if (typeof actualAlignment === 'number') {
          normalizedActual = reverseAlignmentMap[actualAlignment] || null
        } else if (typeof actualAlignment === 'string') {
          normalizedActual = actualAlignment.toLowerCase()
        }

        // 更严格的验证：必须明确设置了对齐方式且匹配期望值
        const isMatch = normalizedActual !== null && normalizedActual === expectedAlignment

        validationResults.push({
          cell: cellConfig.cell,
          expected: expectedAlignment,
          actual: normalizedActual || '(未设置)',
          match: isMatch
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '单元格对齐验证通过！',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.cell}: 期望 ${result.expected}, 实际 ${result.actual}`
        ).join('; ')

        return {
          success: false,
          message: `单元格对齐不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }
    } catch (error) {
      console.error('❌ 验证多单元格对齐时发生错误:', error)
      return {
        success: false,
        message: `验证多单元格对齐时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证多边框
   */
  async validateMultiBorder(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cells || !Array.isArray(rule.cells)) {
        return {
          success: false,
          message: '验证规则配置错误：缺少边框配置'
        }
      }

      console.log('🔍 开始验证多边框:', rule)

      const validationResults = []

      for (const cellConfig of rule.cells) {
        if (!cellConfig.range || !cellConfig.expectedBorder) {
          validationResults.push({
            range: cellConfig.range || '未知',
            expected: cellConfig.expectedBorder || '未知',
            actual: '配置错误',
            match: false
          })
          continue
        }

        // 解析范围
        const [startCell, endCell] = cellConfig.range.split(':')
        const startRange = worksheet.getRange(startCell)
        const endRange = endCell ? worksheet.getRange(endCell) : startRange

        // 检查边框设置
        let hasBorder = false
        let borderType = 'none'
        let borderDetails = null

        // 检查起始单元格的边框
        const startCellData = startRange.getCellData()
        const startStyle = startCellData.s || {}

        console.log(`🔲 范围 ${cellConfig.range} 边框验证:`, {
          expectedBorder: cellConfig.expectedBorder,
          expectedBorderColor: cellConfig.expectedBorderColor,
          startStyle
        })

        // 检查边框属性 (bd: border)
        if (startStyle.bd) {
          borderDetails = startStyle.bd
          hasBorder = true

          // 更详细的边框类型检查
          const borderInfo = this.analyzeBorderStyle(startStyle.bd, cellConfig.expectedBorder)
          borderType = borderInfo.type

          // 如果期望特定颜色，检查颜色
          if (cellConfig.expectedBorderColor) {
            const colorMatch = this.checkBorderColor(startStyle.bd, cellConfig.expectedBorderColor)
            if (!colorMatch) {
              borderType = 'wrong_color'
            }
          }
        }

        // 更严格的匹配逻辑
        const isMatch = hasBorder && borderType === cellConfig.expectedBorder

        validationResults.push({
          range: cellConfig.range,
          expected: cellConfig.expectedBorder,
          actual: hasBorder ? borderType : 'none',
          match: isMatch,
          borderDetails: borderDetails
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '边框验证通过！',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.range}: 期望 ${result.expected}, 实际 ${result.actual}`
        ).join('; ')

        return {
          success: false,
          message: `边框设置不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }
    } catch (error) {
      console.error('❌ 验证多边框时发生错误:', error)
      return {
        success: false,
        message: `验证多边框时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证数据验证
   */
  async validateDataValidation(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cell) {
        return {
          success: false,
          message: '验证规则配置错误：缺少单元格位置'
        }
      }

      console.log('🔍 开始验证数据验证:', rule)

      const range = worksheet.getRange(rule.cell)
      const cellData = range.getCellData()

      // 检查单元格是否有数据验证设置
      // 在Univer中，数据验证信息可能存储在不同的属性中
      const hasDataValidation = cellData.dv || cellData.dataValidation || false

      console.log('📋 数据验证检查:', {
        cell: rule.cell,
        hasDataValidation,
        cellData,
        expectedType: rule.validationType,
        expectedSource: rule.source
      })

      if (!hasDataValidation) {
        return {
          success: false,
          message: `单元格 ${rule.cell} 未设置数据验证。请设置数据验证规则。`,
          details: {
            cell: rule.cell,
            expectedType: rule.validationType,
            expectedSource: rule.source
          }
        }
      }

      // 如果有数据验证，检查类型和源
      if (rule.validationType) {
        // 这里可以进一步验证数据验证的类型和源
        // 由于Univer的数据验证API可能有所不同，我们先简化验证
        return {
          success: true,
          message: '数据验证设置验证通过！',
          details: {
            cell: rule.cell,
            validationType: rule.validationType,
            source: rule.source,
            hasDataValidation
          }
        }
      }

      return {
        success: true,
        message: '数据验证设置验证通过！',
        details: { cell: rule.cell, hasDataValidation }
      }

    } catch (error) {
      console.error('❌ 验证数据验证时发生错误:', error)
      return {
        success: false,
        message: `验证数据验证时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格合并
   */
  async validateCellMerge(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.mergedRanges || !Array.isArray(rule.mergedRanges)) {
        return {
          success: false,
          message: '验证规则配置错误：缺少合并范围配置'
        }
      }

      console.log('🔍 开始验证单元格合并:', rule)

      const validationResults = []

      for (const mergeConfig of rule.mergedRanges) {
        if (!mergeConfig.range) {
          validationResults.push({
            range: '未知',
            expected: '合并',
            actual: '配置错误',
            match: false
          })
          continue
        }

        // 检查范围是否已合并
        const range = worksheet.getRange(mergeConfig.range)

        // 在Univer中，合并信息可能存储在不同的属性中
        // 我们需要检查范围的合并状态
        let isMerged = false

        try {
          // 尝试获取合并信息
          const mergeInfo = range.getMergedCells ? range.getMergedCells() : null
          isMerged = mergeInfo && mergeInfo.length > 0

          console.log(`🔗 范围 ${mergeConfig.range} 合并检查:`, {
            isMerged,
            mergeInfo,
            description: mergeConfig.description
          })

        } catch (e) {
          // 如果API不可用，尝试其他方法检查
          console.log('使用备用方法检查合并状态')

          // 解析范围
          const [startCell, endCell] = mergeConfig.range.split(':')
          if (startCell && endCell && startCell !== endCell) {
            // 如果是范围且不是单个单元格，假设应该合并
            isMerged = true // 简化处理
          }
        }

        validationResults.push({
          range: mergeConfig.range,
          expected: '合并',
          actual: isMerged ? '已合并' : '未合并',
          match: isMerged,
          description: mergeConfig.description
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '单元格合并验证通过！',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.range}: ${result.description || '未合并'}`
        ).join('; ')

        return {
          success: false,
          message: `单元格合并不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }

    } catch (error) {
      console.error('❌ 验证单元格合并时发生错误:', error)
      return {
        success: false,
        message: `验证单元格合并时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证文本换行
   */
  async validateTextWrap(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cells || !Array.isArray(rule.cells)) {
        return {
          success: false,
          message: '验证规则配置错误：缺少文本换行配置'
        }
      }

      console.log('🔍 开始验证文本换行:', rule)

      const validationResults = []

      for (const cellConfig of rule.cells) {
        if (!cellConfig.cell) {
          validationResults.push({
            cell: '未知',
            expected: cellConfig.wrapType || '换行',
            actual: '配置错误',
            match: false
          })
          continue
        }

        const range = worksheet.getRange(cellConfig.cell)
        const cellData = range.getCellData()
        const style = cellData.s || {}
        const cellValue = cellData.v || ''

        console.log(`📝 单元格 ${cellConfig.cell} 换行验证:`, {
          wrapType: cellConfig.wrapType,
          expectedText: cellConfig.expectedText,
          actualValue: cellValue,
          style
        })

        let isValid = false

        if (cellConfig.wrapType === 'auto') {
          // 检查自动换行设置
          const hasWrapText = style.tb === 1 || style.wrapText === true || style.wrap === true
          isValid = hasWrapText

          validationResults.push({
            cell: cellConfig.cell,
            expected: '自动换行',
            actual: hasWrapText ? '已设置自动换行' : '未设置自动换行',
            match: isValid,
            description: cellConfig.description
          })

        } else if (cellConfig.wrapType === 'manual' && cellConfig.expectedText) {
          // 检查强制换行内容
          const expectedText = cellConfig.expectedText
          const actualText = String(cellValue)

          // 检查是否包含换行符
          const hasLineBreak = actualText.includes('\n') || actualText.includes('\r')
          const textMatch = actualText === expectedText

          isValid = hasLineBreak && textMatch

          validationResults.push({
            cell: cellConfig.cell,
            expected: `强制换行: ${expectedText}`,
            actual: `实际内容: ${actualText}`,
            match: isValid,
            description: cellConfig.description
          })
        }
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '文本换行验证通过！',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.cell}: ${result.description || result.expected}`
        ).join('; ')

        return {
          success: false,
          message: `文本换行设置不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }

    } catch (error) {
      console.error('❌ 验证文本换行时发生错误:', error)
      return {
        success: false,
        message: `验证文本换行时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证公式填充
   */
  async validateFormulaFill(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.formulaRange || !rule.expectedFormulas) {
        return {
          success: false,
          message: '验证规则配置错误：缺少公式范围或期望公式配置'
        }
      }

      console.log('🔍 开始验证公式填充:', rule)

      const validationResults = []

      // 解析公式范围
      const [startCell, endCell] = rule.formulaRange.split(':')
      if (!startCell || !endCell) {
        return {
          success: false,
          message: '公式范围格式错误'
        }
      }

      // 获取范围内的所有单元格
      const range = worksheet.getRange(rule.formulaRange)
      const rangeData = range.getCellData()

      console.log('📊 公式填充验证:', {
        formulaRange: rule.formulaRange,
        expectedFormulas: rule.expectedFormulas,
        rangeData
      })

      // 验证每个期望的公式
      for (const [cellAddress, expectedFormula] of Object.entries(rule.expectedFormulas)) {
        const cellRange = worksheet.getRange(cellAddress)
        const cellData = cellRange.getCellData()

        // 获取单元格的公式
        const actualFormula = cellData.f || cellData.formula || ''
        const cellValue = cellData.v || ''

        console.log(`🧮 单元格 ${cellAddress} 公式验证:`, {
          expectedFormula,
          actualFormula,
          cellValue
        })

        // 标准化公式比较（移除空格，统一大小写）
        const normalizedExpected = String(expectedFormula).replace(/\s+/g, '').toUpperCase()
        const normalizedActual = String(actualFormula).replace(/\s+/g, '').toUpperCase()

        const isMatch = normalizedActual === normalizedExpected ||
                       (normalizedActual.includes(normalizedExpected) && normalizedExpected.length > 0)

        validationResults.push({
          cell: cellAddress,
          expected: expectedFormula,
          actual: actualFormula || '(无公式)',
          value: cellValue,
          match: isMatch
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '公式填充验证通过！所有公式都正确填充。',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result =>
          `${result.cell}: 期望 ${result.expected}, 实际 ${result.actual}`
        ).join('; ')

        return {
          success: false,
          message: `公式填充不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }

    } catch (error) {
      console.error('❌ 验证公式填充时发生错误:', error)
      return {
        success: false,
        message: `验证公式填充时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 分析边框样式
   */
  private analyzeBorderStyle(borderData: any, expectedType: string): { type: string } {
    if (!borderData) {
      return { type: 'none' }
    }

    console.log('🔍 分析边框样式:', { borderData, expectedType })

    // 检查边框的各个方向
    const hasTop = borderData.t || borderData.top
    const hasBottom = borderData.b || borderData.bottom
    const hasLeft = borderData.l || borderData.left
    const hasRight = borderData.r || borderData.right

    const borderCount = [hasTop, hasBottom, hasLeft, hasRight].filter(Boolean).length

    // 根据边框数量和期望类型判断
    if (expectedType === 'outline') {
      // 外边框：至少要有上下左右边框
      return { type: borderCount >= 4 ? 'outline' : 'partial' }
    } else if (expectedType === 'all') {
      // 所有边框：检查是否有内部边框标识
      return { type: borderCount >= 4 ? 'all' : 'partial' }
    } else if (expectedType === 'thick') {
      // 粗边框：检查边框粗细
      const isThick = this.checkBorderThickness(borderData)
      return { type: isThick && borderCount >= 4 ? 'thick' : 'thin' }
    }

    return { type: borderCount > 0 ? 'unknown' : 'none' }
  }

  /**
   * 检查边框粗细
   */
  private checkBorderThickness(borderData: any): boolean {
    const checkSide = (side: any) => {
      if (!side) return false
      // 检查边框样式是否为粗边框
      return side.s === 'thick' || side.style === 'thick' || side.width > 1
    }

    return checkSide(borderData.t) || checkSide(borderData.b) ||
           checkSide(borderData.l) || checkSide(borderData.r)
  }

  /**
   * 检查边框颜色
   */
  private checkBorderColor(borderData: any, expectedColor: string): boolean {
    if (!borderData || !expectedColor) return true

    const normalizeColor = (color: any): string => {
      if (!color) return ''
      if (typeof color === 'string' && color.startsWith('#')) {
        return color.toLowerCase()
      }
      if (typeof color === 'object' && color.rgb) {
        return color.rgb.toLowerCase()
      }
      return String(color).toLowerCase()
    }

    const expectedNormalized = normalizeColor(expectedColor)

    // 检查各个边的颜色
    const sides = [borderData.t, borderData.b, borderData.l, borderData.r]
    return sides.some(side => {
      if (!side) return false
      const actualColor = normalizeColor(side.cl || side.color)
      return actualColor === expectedNormalized
    })
  }
}
