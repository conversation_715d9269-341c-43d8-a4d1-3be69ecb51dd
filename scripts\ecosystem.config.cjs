// ecosystem.config.cjs
const path = require('path');
const projectDir = path.resolve(__dirname);
const logDir = path.join(projectDir, 'logs');

// 确保日志目录存在
require('fs').mkdirSync(logDir, { recursive: true });

module.exports = {
  apps: [{
    name: "svelte-app",
    
    // 关键变化：直接指向构建产物
    script: "build/index.js", 
    
    cwd: projectDir,
    env: {
      PORT: 4000,
      ORIGIN: "http://**************:4000",
      NODE_ENV: "production",
      DATABASE_URL: "postgresql://nextjs:RhC4UG7NT.6X6vd@localhost:5432/learn_excel-nextjs",
      AUTH_SECRET: "a-very-long-and-secure-secret-for-testing-purposes-only",
      PUBLIC_BASE_URL: "http://**************:4000"
    },
    env_file: ".env.local",      // 环境变量文件
    
    // 日志配置
    log_date_format: "YYYY-MM-DD HH:mm:ss",
    out_file: path.join(logDir, "app-out.log"),
    error_file: path.join(logDir, "app-error.log"),
    combine_logs: true,
    
    // 进程管理
    instances: 1,
    exec_mode: "cluster",
    autorestart: true,
    watch: false,
    max_memory_restart: "1G",
    min_uptime: "60s"
  }]
};
